#!/usr/bin/env python3
"""
分析包含'quality'属性的样本，特别是quality为合格的样本
用于优化角点拟合算法
"""

import os
import json
import math
import numpy as np
from pathlib import Path
from typing import List, Dict, Tuple, Optional


def find_quality_samples(search_dirs: List[str]) -> List[Dict]:
    """
    查找包含quality属性的样本文件
    
    Args:
        search_dirs: 搜索目录列表
        
    Returns:
        包含quality信息的样本列表
    """
    quality_samples = []
    
    for search_dir in search_dirs:
        search_path = Path(search_dir)
        if not search_path.exists():
            print(f"目录不存在: {search_dir}")
            continue
            
        print(f"搜索目录: {search_dir}")
        
        # 查找所有JSON文件
        json_files = list(search_path.glob("**/*.json"))
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 检查顶层quality属性
                if 'quality' in data:
                    quality_samples.append({
                        'file': str(json_file),
                        'quality': data['quality'],
                        'type': data.get('type', None),
                        'cell_count': len(data.get('cells', [])),
                        'data': data
                    })
                    print(f"  找到quality样本: {json_file.name} (quality: {data['quality']})")
                
                # 检查单元格级别的quality属性
                cells = data.get('cells', [])
                for i, cell in enumerate(cells):
                    if 'quality' in cell:
                        quality_samples.append({
                            'file': str(json_file),
                            'quality': cell['quality'],
                            'type': data.get('type', None),
                            'cell_index': i,
                            'cell_count': len(cells),
                            'data': data
                        })
                        print(f"  找到单元格quality样本: {json_file.name} cell[{i}] (quality: {cell['quality']})")
                        
            except Exception as e:
                # 忽略无法读取的文件
                pass
    
    return quality_samples


def analyze_good_quality_samples(quality_samples: List[Dict]) -> Dict:
    """
    分析quality为合格的样本特征
    
    Args:
        quality_samples: 包含quality信息的样本列表
        
    Returns:
        分析结果
    """
    print("\n" + "=" * 60)
    print("分析quality为合格的样本")
    print("=" * 60)
    
    # 筛选出合格的样本
    good_samples = []
    quality_values = set()
    
    for sample in quality_samples:
        quality = sample['quality']
        quality_values.add(str(quality))
        
        # 判断是否为合格样本（可能的合格值）
        if quality in ['good', 'qualified', 'pass', 'ok', 1, True, '合格', '良好']:
            good_samples.append(sample)
    
    print(f"发现的quality值: {sorted(quality_values)}")
    print(f"合格样本数量: {len(good_samples)}")
    print(f"总样本数量: {len(quality_samples)}")
    
    if not good_samples:
        print("❌ 未找到合格的quality样本")
        return {}
    
    # 分析合格样本的特征
    analysis_results = {
        'sample_count': len(good_samples),
        'cell_counts': [],
        'point_distances': [],
        'alignment_errors': [],
        'geometric_features': []
    }
    
    for sample in good_samples:
        data = sample['data']
        cells = data.get('cells', [])
        
        if not cells:
            continue
            
        print(f"\n分析样本: {Path(sample['file']).name}")
        
        # 分析单元格数量
        cell_count = len(cells)
        analysis_results['cell_counts'].append(cell_count)
        print(f"  单元格数量: {cell_count}")
        
        # 分析角点分布
        point_analysis = analyze_point_distribution(cells)
        analysis_results['point_distances'].extend(point_analysis['distances'])
        
        # 分析对齐质量
        alignment_analysis = analyze_alignment_quality(cells)
        analysis_results['alignment_errors'].extend(alignment_analysis['errors'])
        
        # 分析几何特征
        geometric_analysis = analyze_geometric_features(cells)
        analysis_results['geometric_features'].append(geometric_analysis)
        
        print(f"  最小角点距离: {point_analysis['min_distance']:.2f}px")
        print(f"  平均对齐误差: {alignment_analysis['avg_error']:.2f}px")
        print(f"  表格规整度: {geometric_analysis['regularity']:.3f}")
    
    return analysis_results


def analyze_point_distribution(cells: List[Dict]) -> Dict:
    """分析角点分布"""
    all_points = []
    for cell in cells:
        bbox = cell.get('bbox', {})
        for point_name in ['p1', 'p2', 'p3', 'p4']:
            if point_name in bbox:
                all_points.append(bbox[point_name])
    
    if len(all_points) < 2:
        return {'distances': [], 'min_distance': 0}
    
    # 计算所有点对之间的距离
    distances = []
    for i, p1 in enumerate(all_points):
        for j, p2 in enumerate(all_points):
            if i < j:  # 避免重复计算
                dist = math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                if dist > 0:
                    distances.append(dist)
    
    return {
        'distances': distances,
        'min_distance': min(distances) if distances else 0,
        'median_distance': np.median(distances) if distances else 0
    }


def analyze_alignment_quality(cells: List[Dict]) -> Dict:
    """分析对齐质量"""
    errors = []
    
    # 按行分组
    rows = {}
    for cell in cells:
        lloc = cell.get('lloc', {})
        row = lloc.get('start_row', -1)
        if row not in rows:
            rows[row] = []
        rows[row].append(cell)
    
    # 分析每行的水平对齐
    for row_cells in rows.values():
        if len(row_cells) > 1:
            # 检查上边界对齐
            top_coords = []
            bottom_coords = []
            for cell in row_cells:
                bbox = cell.get('bbox', {})
                if 'p1' in bbox and 'p2' in bbox:
                    top_coords.extend([bbox['p1'][1], bbox['p2'][1]])
                if 'p3' in bbox and 'p4' in bbox:
                    bottom_coords.extend([bbox['p3'][1], bbox['p4'][1]])
            
            if len(top_coords) > 1:
                errors.append(max(top_coords) - min(top_coords))
            if len(bottom_coords) > 1:
                errors.append(max(bottom_coords) - min(bottom_coords))
    
    return {
        'errors': errors,
        'avg_error': np.mean(errors) if errors else 0,
        'max_error': max(errors) if errors else 0
    }


def analyze_geometric_features(cells: List[Dict]) -> Dict:
    """分析几何特征"""
    if not cells:
        return {'regularity': 0}
    
    # 计算单元格的规整度
    regularities = []
    
    for cell in cells:
        bbox = cell.get('bbox', {})
        if all(p in bbox for p in ['p1', 'p2', 'p3', 'p4']):
            # 计算四边形的规整度（接近矩形的程度）
            p1, p2, p3, p4 = [bbox[p] for p in ['p1', 'p2', 'p3', 'p4']]
            
            # 计算四条边的长度
            side1 = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
            side2 = math.sqrt((p3[0] - p2[0])**2 + (p3[1] - p2[1])**2)
            side3 = math.sqrt((p4[0] - p3[0])**2 + (p4[1] - p3[1])**2)
            side4 = math.sqrt((p1[0] - p4[0])**2 + (p1[1] - p4[1])**2)
            
            # 对边长度比
            if side1 > 0 and side3 > 0:
                ratio1 = min(side1, side3) / max(side1, side3)
            else:
                ratio1 = 0
                
            if side2 > 0 and side4 > 0:
                ratio2 = min(side2, side4) / max(side2, side4)
            else:
                ratio2 = 0
            
            regularity = (ratio1 + ratio2) / 2
            regularities.append(regularity)
    
    return {
        'regularity': np.mean(regularities) if regularities else 0,
        'cell_regularities': regularities
    }


def generate_optimization_recommendations(analysis_results: Dict) -> List[str]:
    """基于分析结果生成优化建议"""
    recommendations = []
    
    if not analysis_results:
        return ["无法生成建议：缺少分析数据"]
    
    # 基于角点距离分析
    if analysis_results['point_distances']:
        distances = analysis_results['point_distances']
        min_dist = min(distances)
        median_dist = np.median(distances)
        
        if min_dist < 2.0:
            recommendations.append(f"建议使用更小的基础阈值（如1.0-1.5px），因为最小角点距离为{min_dist:.2f}px")
        elif min_dist > 10.0:
            recommendations.append(f"可以使用较大的基础阈值（如5.0-8.0px），因为最小角点距离为{min_dist:.2f}px")
        else:
            recommendations.append(f"建议使用中等阈值（如2.0-3.0px），最小角点距离为{min_dist:.2f}px")
    
    # 基于对齐误差分析
    if analysis_results['alignment_errors']:
        errors = analysis_results['alignment_errors']
        avg_error = np.mean(errors)
        max_error = max(errors)
        
        if avg_error < 1.0:
            recommendations.append("合格样本的对齐误差很小，建议使用精确的微调策略")
        elif avg_error > 5.0:
            recommendations.append("合格样本的对齐误差较大，建议使用更宽松的对齐策略")
    
    # 基于几何特征分析
    if analysis_results['geometric_features']:
        regularities = [gf['regularity'] for gf in analysis_results['geometric_features']]
        avg_regularity = np.mean(regularities)
        
        if avg_regularity > 0.9:
            recommendations.append("合格样本的几何规整度很高，建议保持透视变换特性")
        elif avg_regularity < 0.7:
            recommendations.append("合格样本的几何规整度较低，建议增强透视变换处理")
    
    return recommendations


def main():
    """主函数"""
    print("🔍 分析包含'quality'属性的样本")
    print("=" * 60)
    
    # 搜索目录列表
    search_dirs = [
        "organized_dataset",
        "borderless_table",
        "borderless_table_optimized",
        "adaptive_test_results",
        ".",  # 当前目录
    ]
    
    # 查找quality样本
    quality_samples = find_quality_samples(search_dirs)
    
    if not quality_samples:
        print("❌ 未找到包含'quality'属性的样本文件")
        print("建议检查以下目录是否存在quality属性的标注文件：")
        for dir_path in search_dirs:
            print(f"  - {dir_path}")
        return 1
    
    # 分析合格样本
    analysis_results = analyze_good_quality_samples(quality_samples)
    
    if analysis_results:
        # 生成优化建议
        print("\n" + "=" * 60)
        print("基于合格样本的优化建议")
        print("=" * 60)
        
        recommendations = generate_optimization_recommendations(analysis_results)
        for i, rec in enumerate(recommendations, 1):
            print(f"{i}. {rec}")
        
        # 输出统计信息
        print(f"\n📊 统计信息:")
        print(f"合格样本数量: {analysis_results['sample_count']}")
        if analysis_results['cell_counts']:
            print(f"平均单元格数: {np.mean(analysis_results['cell_counts']):.1f}")
        if analysis_results['point_distances']:
            print(f"角点距离中位数: {np.median(analysis_results['point_distances']):.2f}px")
        if analysis_results['alignment_errors']:
            print(f"平均对齐误差: {np.mean(analysis_results['alignment_errors']):.2f}px")
    
    return 0


if __name__ == "__main__":
    exit(main())
