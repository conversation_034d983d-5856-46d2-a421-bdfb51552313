#!/usr/bin/env python3
"""
角度校正和线条一致性优化器

专门解决图片旋转导致的表格线角度分叉问题：
1. 检测图片旋转角度
2. 校正表格线角度一致性
3. 减少从同一坐标点分出的线条角度分叉
"""

import os
import json
import math
import numpy as np
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from PIL import Image
import cv2


class AngleCorrectionOptimizer:
    """角度校正优化器"""
    
    def __init__(self, angle_tolerance: float = 2.0, line_merge_threshold: float = 3.0):
        """
        初始化角度校正优化器
        
        Args:
            angle_tolerance: 角度容差（度）
            line_merge_threshold: 线条合并阈值（像素）
        """
        self.angle_tolerance = angle_tolerance
        self.line_merge_threshold = line_merge_threshold
        self.detected_rotation = 0.0
        self.cells = []
        self.image_info = None
        
    def detect_image_rotation(self, image_path: str) -> float:
        """
        检测图片旋转角度
        
        Args:
            image_path: 图片路径
            
        Returns:
            检测到的旋转角度（度）
        """
        try:
            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                print(f"无法读取图片: {image_path}")
                return 0.0
                
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 边缘检测
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)
            
            # 霍夫线变换检测直线
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
            
            if lines is None:
                print("未检测到足够的直线")
                return 0.0
            
            # 分析线条角度
            angles = []
            for line in lines:
                rho, theta = line[0]
                angle = theta * 180 / np.pi
                
                # 将角度标准化到0-90度范围
                if angle > 90:
                    angle = angle - 180
                elif angle < -90:
                    angle = angle + 180
                    
                angles.append(angle)
            
            # 找到主要的水平和垂直方向
            horizontal_angles = [a for a in angles if abs(a) < 45]
            vertical_angles = [a for a in angles if abs(a - 90) < 45 or abs(a + 90) < 45]
            
            # 计算平均旋转角度
            if horizontal_angles:
                avg_horizontal = np.mean(horizontal_angles)
                rotation_angle = -avg_horizontal  # 负号表示需要反向旋转来校正
                print(f"检测到图片旋转角度: {rotation_angle:.2f}度")
                return rotation_angle
            
            return 0.0
            
        except Exception as e:
            print(f"角度检测失败: {e}")
            return 0.0
    
    def calculate_line_angle(self, p1: List[float], p2: List[float]) -> float:
        """
        计算两点间直线的角度
        
        Args:
            p1: 起点坐标
            p2: 终点坐标
            
        Returns:
            角度（度）
        """
        dx = p2[0] - p1[0]
        dy = p2[1] - p1[1]
        
        if abs(dx) < 1e-6:  # 垂直线
            return 90.0
        
        angle = math.atan2(dy, dx) * 180 / math.pi
        return angle
    
    def find_connected_lines(self, cell_bbox: Dict) -> List[Dict]:
        """
        找到单元格边框的连接线信息
        
        Args:
            cell_bbox: 单元格边框
            
        Returns:
            连接线信息列表
        """
        points = ['p1', 'p2', 'p3', 'p4']
        lines = []
        
        # 定义边的连接关系
        edges = [
            ('p1', 'p2', 'top'),     # 上边
            ('p2', 'p3', 'right'),   # 右边
            ('p3', 'p4', 'bottom'),  # 下边
            ('p4', 'p1', 'left')     # 左边
        ]
        
        for start_point, end_point, edge_type in edges:
            if start_point in cell_bbox and end_point in cell_bbox:
                p1 = cell_bbox[start_point]
                p2 = cell_bbox[end_point]
                angle = self.calculate_line_angle(p1, p2)
                
                lines.append({
                    'start': start_point,
                    'end': end_point,
                    'start_coords': p1,
                    'end_coords': p2,
                    'angle': angle,
                    'edge_type': edge_type,
                    'length': math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
                })
        
        return lines
    
    def group_lines_by_direction(self, all_lines: List[Dict]) -> Dict[str, List[Dict]]:
        """
        按方向分组线条
        
        Args:
            all_lines: 所有线条
            
        Returns:
            按方向分组的线条字典
        """
        horizontal_lines = []
        vertical_lines = []
        
        for line in all_lines:
            angle = line['angle']
            
            # 判断是水平线还是垂直线
            if abs(angle) < 45 or abs(angle) > 135:
                horizontal_lines.append(line)
            else:
                vertical_lines.append(line)
        
        return {
            'horizontal': horizontal_lines,
            'vertical': vertical_lines
        }
    
    def correct_line_angles(self, lines: List[Dict], target_angle: float) -> List[Dict]:
        """
        校正线条角度到目标角度
        
        Args:
            lines: 线条列表
            target_angle: 目标角度
            
        Returns:
            校正后的线条列表
        """
        corrected_lines = []
        
        for line in lines:
            current_angle = line['angle']
            angle_diff = abs(current_angle - target_angle)
            
            # 如果角度差异在容差范围内，进行校正
            if angle_diff <= self.angle_tolerance:
                # 计算校正后的终点坐标
                start_coords = line['start_coords']
                length = line['length']
                
                # 使用目标角度重新计算终点
                target_angle_rad = target_angle * math.pi / 180
                new_end_x = start_coords[0] + length * math.cos(target_angle_rad)
                new_end_y = start_coords[1] + length * math.sin(target_angle_rad)
                
                corrected_line = line.copy()
                corrected_line['end_coords'] = [new_end_x, new_end_y]
                corrected_line['angle'] = target_angle
                corrected_line['corrected'] = True
                
                corrected_lines.append(corrected_line)
            else:
                corrected_lines.append(line)
        
        return corrected_lines
    
    def find_shared_points(self) -> Dict[str, List[Dict]]:
        """
        找到共享的角点
        
        Returns:
            共享点字典，键为坐标字符串，值为使用该点的单元格和角点信息
        """
        shared_points = {}
        
        for cell_idx, cell in enumerate(self.cells):
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                if point_name in bbox:
                    coords = bbox[point_name]
                    coord_key = f"{coords[0]:.1f},{coords[1]:.1f}"
                    
                    if coord_key not in shared_points:
                        shared_points[coord_key] = []
                    
                    shared_points[coord_key].append({
                        'cell_idx': cell_idx,
                        'point_name': point_name,
                        'coords': coords
                    })
        
        # 只返回被多个单元格共享的点
        return {k: v for k, v in shared_points.items() if len(v) > 1}
    
    def correct_shared_point_angles(self):
        """
        校正共享点的角度一致性
        """
        shared_points = self.find_shared_points()
        corrected_count = 0
        
        print(f"发现 {len(shared_points)} 个共享角点")
        
        for coord_key, point_info in shared_points.items():
            if len(point_info) < 2:
                continue
            
            # 收集从该点出发的所有线条
            outgoing_lines = []
            
            for info in point_info:
                cell = self.cells[info['cell_idx']]
                bbox = cell['bbox']
                point_name = info['point_name']
                
                # 找到从该点出发的线条
                if point_name == 'p1':
                    # p1连接到p2和p4
                    if 'p2' in bbox:
                        outgoing_lines.append({
                            'cell_idx': info['cell_idx'],
                            'start_point': 'p1',
                            'end_point': 'p2',
                            'direction': 'horizontal'
                        })
                    if 'p4' in bbox:
                        outgoing_lines.append({
                            'cell_idx': info['cell_idx'],
                            'start_point': 'p1',
                            'end_point': 'p4',
                            'direction': 'vertical'
                        })
                # 类似地处理其他点...
            
            # 按方向分组并校正角度
            horizontal_lines = [l for l in outgoing_lines if l['direction'] == 'horizontal']
            vertical_lines = [l for l in outgoing_lines if l['direction'] == 'vertical']
            
            # 校正水平线角度一致性
            if len(horizontal_lines) > 1:
                corrected_count += self._align_lines_angle(horizontal_lines, 'horizontal')
            
            # 校正垂直线角度一致性
            if len(vertical_lines) > 1:
                corrected_count += self._align_lines_angle(vertical_lines, 'vertical')
        
        print(f"校正了 {corrected_count} 条线的角度")
    
    def _align_lines_angle(self, lines: List[Dict], direction: str) -> int:
        """
        对齐同方向线条的角度
        
        Args:
            lines: 线条列表
            direction: 方向（'horizontal' 或 'vertical'）
            
        Returns:
            校正的线条数量
        """
        if len(lines) < 2:
            return 0
        
        # 计算所有线条的角度
        angles = []
        for line_info in lines:
            cell = self.cells[line_info['cell_idx']]
            bbox = cell['bbox']
            start_coords = bbox[line_info['start_point']]
            end_coords = bbox[line_info['end_point']]
            angle = self.calculate_line_angle(start_coords, end_coords)
            angles.append(angle)
        
        # 计算平均角度作为目标角度
        target_angle = np.mean(angles)
        
        # 如果是水平线，目标角度应该接近0或180
        if direction == 'horizontal':
            if abs(target_angle) > 90:
                target_angle = 180 if target_angle > 0 else -180
            else:
                target_angle = 0
        # 如果是垂直线，目标角度应该接近90或-90
        elif direction == 'vertical':
            target_angle = 90 if target_angle > 0 else -90
        
        # 校正每条线的终点
        corrected_count = 0
        for line_info in lines:
            cell = self.cells[line_info['cell_idx']]
            bbox = cell['bbox']
            start_coords = bbox[line_info['start_point']]
            end_coords = bbox[line_info['end_point']]
            
            current_angle = self.calculate_line_angle(start_coords, end_coords)
            angle_diff = abs(current_angle - target_angle)
            
            if angle_diff > self.angle_tolerance:
                # 重新计算终点坐标
                length = math.sqrt((end_coords[0] - start_coords[0])**2 + 
                                 (end_coords[1] - start_coords[1])**2)
                
                target_angle_rad = target_angle * math.pi / 180
                new_end_x = start_coords[0] + length * math.cos(target_angle_rad)
                new_end_y = start_coords[1] + length * math.sin(target_angle_rad)
                
                # 更新坐标
                bbox[line_info['end_point']] = [new_end_x, new_end_y]
                corrected_count += 1
        
        return corrected_count
    
    def optimize_table_angles(self, annotation_file: str, output_file: str, 
                            image_file: Optional[str] = None) -> Dict:
        """
        优化表格角度一致性
        
        Args:
            annotation_file: 输入标注文件
            output_file: 输出文件
            image_file: 对应图片文件
            
        Returns:
            处理结果
        """
        try:
            print(f"开始角度校正优化: {Path(annotation_file).name}")
            
            # 加载标注数据
            with open(annotation_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.cells = data['cells']
            
            # 检测图片旋转角度
            if image_file and Path(image_file).exists():
                self.detected_rotation = self.detect_image_rotation(image_file)
                
                # 获取图片信息
                with Image.open(image_file) as img:
                    self.image_info = {'width': img.size[0], 'height': img.size[1]}
            
            print(f"加载了 {len(self.cells)} 个单元格")
            
            # 校正共享点角度一致性
            print("校正共享点角度一致性...")
            self.correct_shared_point_angles()
            
            # 保存结果
            output_data = data.copy()
            output_data['cells'] = self.cells
            
            # 添加角度校正信息
            if 'angle_correction' not in output_data:
                output_data['angle_correction'] = {}
            
            output_data['angle_correction'].update({
                'detected_rotation': self.detected_rotation,
                'angle_tolerance': self.angle_tolerance,
                'corrected': True
            })
            
            # 确保输出目录存在
            output_dir = os.path.dirname(output_file)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            
            print(f"角度校正完成，结果保存到: {output_file}")
            
            return {
                'success': True,
                'cell_count': len(self.cells),
                'detected_rotation': self.detected_rotation,
                'angle_tolerance': self.angle_tolerance
            }
            
        except Exception as e:
            error_msg = f"角度校正失败: {e}"
            print(error_msg)
            return {
                'success': False,
                'error': error_msg
            }


def test_angle_correction():
    """测试角度校正功能"""
    print("🔧 测试角度校正优化器")
    print("=" * 50)
    
    # 查找测试文件
    test_dirs = [
        "F:\\workspace\\datasets\\Relabel_TabRecSet\\chinese\\curved_tables",
        "borderless_table",
        "organized_dataset"
    ]
    
    test_files = []
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            test_files.extend(list(Path(test_dir).glob("*.json"))[:3])
            break
    
    if not test_files:
        print("❌ 未找到测试文件")
        return
    
    # 创建角度校正优化器
    optimizer = AngleCorrectionOptimizer(
        angle_tolerance=2.0,
        line_merge_threshold=3.0
    )
    
    for test_file in test_files:
        print(f"\n测试文件: {test_file.name}")
        print("-" * 30)
        
        # 查找对应图片
        image_file = None
        for ext in ['.jpg', '.jpeg', '.png']:
            img_path = test_file.parent / f"{test_file.stem.replace('_table_annotation', '')}{ext}"
            if img_path.exists():
                image_file = str(img_path)
                break
        
        # 执行角度校正
        output_file = f"angle_corrected_{test_file.name}"
        result = optimizer.optimize_table_angles(
            str(test_file),
            output_file,
            image_file
        )
        
        if result['success']:
            print(f"✅ 角度校正成功")
            print(f"📊 单元格数: {result['cell_count']}")
            print(f"🔄 检测旋转角度: {result['detected_rotation']:.2f}度")
            print(f"🎯 角度容差: {result['angle_tolerance']}度")
        else:
            print(f"❌ 角度校正失败: {result['error']}")


if __name__ == "__main__":
    test_angle_correction()
