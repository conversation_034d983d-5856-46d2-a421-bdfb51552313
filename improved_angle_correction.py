#!/usr/bin/env python3
"""
改进的角度校正算法

专门针对减少从同一坐标点分出的表格线小角度分叉问题
"""

import os
import json
import math
import numpy as np
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from table_annotation_optimizer import PerspectiveAwareOptimizer


class ImprovedAngleCorrectionOptimizer(PerspectiveAwareOptimizer):
    """改进的角度校正优化器"""
    
    def __init__(self, tolerance: float = 2.0, preserve_perspective: bool = True,
                 adaptive_threshold: bool = True, quality_aware: bool = True,
                 angle_correction: bool = True, strict_angle_correction: bool = True):
        """
        初始化改进的角度校正优化器
        
        Args:
            strict_angle_correction: 是否启用严格角度校正
        """
        super().__init__(tolerance, preserve_perspective, adaptive_threshold, 
                         quality_aware, angle_correction)
        self.strict_angle_correction = strict_angle_correction
        self.angle_snap_threshold = 5.0  # 角度吸附阈值（度）
        
    def detect_angle_bifurcation(self) -> List[Dict]:
        """
        检测角度分叉问题
        
        Returns:
            分叉点信息列表
        """
        bifurcation_points = []
        shared_points = self.find_shared_points()
        
        for coord_key, point_info in shared_points.items():
            if len(point_info) < 2:
                continue
                
            # 收集从该点出发的所有线条
            outgoing_lines = []
            
            for info in point_info:
                cell = self.cells[info['cell_idx']]
                bbox = cell['bbox']
                point_name = info['point_name']
                coords = info['coords']
                
                # 找到从该点出发的线条
                connected_points = self._get_connected_points(point_name, bbox)
                
                for connected_point, direction in connected_points:
                    if connected_point in bbox:
                        target_coords = bbox[connected_point]
                        angle = self.calculate_line_angle(coords, target_coords)
                        
                        outgoing_lines.append({
                            'cell_idx': info['cell_idx'],
                            'start_point': point_name,
                            'end_point': connected_point,
                            'start_coords': coords,
                            'end_coords': target_coords,
                            'angle': angle,
                            'direction': direction,
                            'length': math.sqrt((target_coords[0] - coords[0])**2 + 
                                              (target_coords[1] - coords[1])**2)
                        })
            
            # 检查是否存在角度分叉
            if len(outgoing_lines) > 1:
                bifurcation_info = self._analyze_bifurcation(coord_key, outgoing_lines)
                if bifurcation_info['has_bifurcation']:
                    bifurcation_points.append(bifurcation_info)
        
        return bifurcation_points
    
    def _analyze_bifurcation(self, coord_key: str, lines: List[Dict]) -> Dict:
        """
        分析角度分叉情况
        
        Args:
            coord_key: 坐标键
            lines: 线条列表
            
        Returns:
            分叉分析结果
        """
        # 按方向分组
        horizontal_lines = [l for l in lines if l['direction'] == 'horizontal']
        vertical_lines = [l for l in lines if l['direction'] == 'vertical']
        
        bifurcation_info = {
            'coord_key': coord_key,
            'has_bifurcation': False,
            'horizontal_bifurcation': False,
            'vertical_bifurcation': False,
            'horizontal_lines': horizontal_lines,
            'vertical_lines': vertical_lines,
            'max_angle_diff': 0.0
        }
        
        # 检查水平线分叉
        if len(horizontal_lines) > 1:
            h_angles = [l['angle'] for l in horizontal_lines]
            h_angle_diff = max(h_angles) - min(h_angles)
            
            # 处理跨越180度的情况
            if h_angle_diff > 180:
                h_angle_diff = 360 - h_angle_diff
                
            if h_angle_diff > self.angle_tolerance:
                bifurcation_info['horizontal_bifurcation'] = True
                bifurcation_info['has_bifurcation'] = True
                bifurcation_info['max_angle_diff'] = max(bifurcation_info['max_angle_diff'], h_angle_diff)
        
        # 检查垂直线分叉
        if len(vertical_lines) > 1:
            v_angles = [l['angle'] for l in vertical_lines]
            v_angle_diff = max(v_angles) - min(v_angles)
            
            # 处理跨越180度的情况
            if v_angle_diff > 180:
                v_angle_diff = 360 - v_angle_diff
                
            if v_angle_diff > self.angle_tolerance:
                bifurcation_info['vertical_bifurcation'] = True
                bifurcation_info['has_bifurcation'] = True
                bifurcation_info['max_angle_diff'] = max(bifurcation_info['max_angle_diff'], v_angle_diff)
        
        return bifurcation_info
    
    def correct_angle_bifurcation(self, bifurcation_points: List[Dict]) -> int:
        """
        校正角度分叉
        
        Args:
            bifurcation_points: 分叉点列表
            
        Returns:
            校正的线条数量
        """
        corrected_count = 0
        
        for bifurcation in bifurcation_points:
            # 校正水平线分叉
            if bifurcation['horizontal_bifurcation']:
                corrected_count += self._correct_direction_bifurcation(
                    bifurcation['horizontal_lines'], 'horizontal'
                )
            
            # 校正垂直线分叉
            if bifurcation['vertical_bifurcation']:
                corrected_count += self._correct_direction_bifurcation(
                    bifurcation['vertical_lines'], 'vertical'
                )
        
        return corrected_count
    
    def _correct_direction_bifurcation(self, lines: List[Dict], direction: str) -> int:
        """
        校正特定方向的角度分叉
        
        Args:
            lines: 线条列表
            direction: 方向
            
        Returns:
            校正的线条数量
        """
        if len(lines) < 2:
            return 0
        
        # 计算目标角度
        angles = [l['angle'] for l in lines]
        
        if direction == 'horizontal':
            # 水平线应该接近0度或180度
            # 选择最接近的标准角度
            target_candidates = [0.0, 180.0]
            target_angle = min(target_candidates, 
                             key=lambda x: min(abs(a - x) for a in angles))
        else:
            # 垂直线应该接近90度或-90度
            target_candidates = [90.0, -90.0]
            target_angle = min(target_candidates, 
                             key=lambda x: min(abs(a - x) for a in angles))
        
        # 如果启用严格角度校正，使用角度吸附
        if self.strict_angle_correction:
            target_angle = self._snap_to_standard_angle(target_angle, direction)
        
        # 校正每条线
        corrected_count = 0
        for line in lines:
            current_angle = line['angle']
            angle_diff = abs(current_angle - target_angle)
            
            # 处理跨越180度的情况
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
            
            if angle_diff > self.angle_tolerance:
                # 重新计算终点坐标
                start_coords = line['start_coords']
                length = line['length']
                
                target_angle_rad = target_angle * math.pi / 180
                new_end_x = start_coords[0] + length * math.cos(target_angle_rad)
                new_end_y = start_coords[1] + length * math.sin(target_angle_rad)
                
                # 更新坐标
                cell = self.cells[line['cell_idx']]
                cell['bbox'][line['end_point']] = [new_end_x, new_end_y]
                corrected_count += 1
        
        return corrected_count
    
    def _snap_to_standard_angle(self, angle: float, direction: str) -> float:
        """
        将角度吸附到标准角度
        
        Args:
            angle: 当前角度
            direction: 方向
            
        Returns:
            吸附后的角度
        """
        if direction == 'horizontal':
            # 水平线吸附到0度或180度
            if abs(angle) < self.angle_snap_threshold:
                return 0.0
            elif abs(angle - 180) < self.angle_snap_threshold:
                return 180.0
            elif abs(angle + 180) < self.angle_snap_threshold:
                return -180.0
        else:
            # 垂直线吸附到90度或-90度
            if abs(angle - 90) < self.angle_snap_threshold:
                return 90.0
            elif abs(angle + 90) < self.angle_snap_threshold:
                return -90.0
        
        return angle
    
    def improved_angle_correction(self):
        """
        执行改进的角度校正
        """
        if not self.angle_correction:
            return
            
        print("执行改进的角度校正...")
        
        # 1. 检测角度分叉
        bifurcation_points = self.detect_angle_bifurcation()
        
        if not bifurcation_points:
            print("  未发现角度分叉问题")
            return
        
        print(f"  发现 {len(bifurcation_points)} 个角度分叉点")
        
        # 2. 校正角度分叉
        corrected_count = self.correct_angle_bifurcation(bifurcation_points)
        
        print(f"  校正了 {corrected_count} 条线的角度分叉")
        
        # 3. 如果启用严格模式，进行全局角度标准化
        if self.strict_angle_correction:
            global_corrected = self._global_angle_standardization()
            print(f"  全局角度标准化校正了 {global_corrected} 条线")
    
    def _global_angle_standardization(self) -> int:
        """
        全局角度标准化
        
        Returns:
            校正的线条数量
        """
        corrected_count = 0
        
        for cell in self.cells:
            bbox = cell['bbox']
            if not all(p in bbox for p in ['p1', 'p2', 'p3', 'p4']):
                continue
            
            # 处理四条边
            edges = [
                ('p1', 'p2', 'horizontal'),  # 上边
                ('p2', 'p3', 'vertical'),    # 右边
                ('p3', 'p4', 'horizontal'),  # 下边
                ('p4', 'p1', 'vertical')     # 左边
            ]
            
            for start_point, end_point, direction in edges:
                start_coords = bbox[start_point]
                end_coords = bbox[end_point]
                
                current_angle = self.calculate_line_angle(start_coords, end_coords)
                target_angle = self._snap_to_standard_angle(current_angle, direction)
                
                angle_diff = abs(current_angle - target_angle)
                if angle_diff > 180:
                    angle_diff = 360 - angle_diff
                
                if angle_diff > self.angle_tolerance:
                    # 重新计算终点
                    length = math.sqrt((end_coords[0] - start_coords[0])**2 + 
                                     (end_coords[1] - start_coords[1])**2)
                    
                    target_angle_rad = target_angle * math.pi / 180
                    new_end_x = start_coords[0] + length * math.cos(target_angle_rad)
                    new_end_y = start_coords[1] + length * math.sin(target_angle_rad)
                    
                    bbox[end_point] = [new_end_x, new_end_y]
                    corrected_count += 1
        
        return corrected_count
    
    def optimize_alignment(self):
        """
        执行改进的角点对齐优化
        """
        print("构建网格结构...")
        self.build_grid_structure()

        print("对齐行边界...")
        self.align_row_boundaries()

        print("对齐列边界...")
        self.align_column_boundaries()

        print("微调相近角点...")
        self.fine_tune_nearby_points()

        # 改进的角度校正步骤
        if self.angle_correction:
            self.improved_angle_correction()

        print("透视感知优化完成！")
        
        # 评估优化质量
        self._evaluate_optimization_quality()


def test_improved_angle_correction():
    """测试改进的角度校正"""
    print("🎯 改进的角度校正测试")
    print("=" * 50)
    
    # 选择测试文件
    test_files = list(Path("borderless_table").glob("*.json"))[:3]
    
    if not test_files:
        print("❌ 未找到测试文件")
        return
    
    for test_file in test_files:
        print(f"\n测试文件: {test_file.name}")
        print("-" * 30)
        
        # 查找对应图片
        image_file = None
        for ext in ['.jpg', '.jpeg', '.png']:
            img_path = test_file.parent / f"{test_file.stem.replace('_table_annotation', '')}{ext}"
            if img_path.exists():
                image_file = str(img_path)
                break
        
        # 创建改进的优化器
        optimizer = ImprovedAngleCorrectionOptimizer(
            tolerance=2.0,
            preserve_perspective=True,
            adaptive_threshold=True,
            quality_aware=True,
            angle_correction=True,
            strict_angle_correction=True
        )
        
        # 执行优化
        output_file = f"improved_angle_{test_file.name}"
        result = optimizer.optimize_table_annotation(
            str(test_file),
            output_file,
            image_file
        )
        
        if result['success']:
            print(f"✅ 改进角度校正成功")
            print(f"📊 单元格数: {result['cell_count']}")
        else:
            print(f"❌ 改进角度校正失败: {result['error']}")


if __name__ == "__main__":
    test_improved_angle_correction()
