#!/usr/bin/env python3
"""
基于quality合格样本分析的优化算法

根据97个合格样本的分析结果，针对性优化角点拟合：
1. 处理重叠角点（最小距离0.00px）
2. 适应较大的对齐误差（平均9.01px）
3. 保持高几何规整度（0.98+）
"""

import os
import json
import math
import numpy as np
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from table_annotation_optimizer import PerspectiveAwareOptimizer


class QualityAwareOptimizer(PerspectiveAwareOptimizer):
    """基于质量样本分析的优化器"""
    
    def __init__(self, tolerance: float = 2.0, preserve_perspective: bool = True,
                 adaptive_threshold: bool = True):
        super().__init__(tolerance, preserve_perspective, adaptive_threshold)
        
        # 基于合格样本分析的特殊参数
        self.overlap_threshold = 0.5  # 重叠角点阈值
        self.quality_tolerance_factor = 1.5  # 质量容差因子
        self.regularity_threshold = 0.95  # 规整度阈值
        
    def calculate_adaptive_threshold(self, img_width: int, img_height: int,
                                   table_width: float, table_height: float) -> float:
        """
        基于质量样本特征的自适应阈值计算
        """
        if not self.adaptive_threshold:
            return self.base_tolerance
            
        # 基础阈值计算
        base_threshold = super().calculate_adaptive_threshold(
            img_width, img_height, table_width, table_height
        )
        
        # 基于质量样本分析的调整
        # 合格样本平均对齐误差为9.01px，需要更宽松的阈值
        quality_factor = self.quality_tolerance_factor
        
        # 考虑角点密度 - 合格样本中很多角点重叠
        cell_count = len(self.cells)
        if cell_count > 0:
            total_area = table_width * table_height if table_width > 0 and table_height > 0 else 1
            density = cell_count / total_area * 10000  # 每万像素的单元格数
            
            if density > 50:  # 高密度表格
                density_factor = 0.8  # 需要更精确
            elif density > 20:  # 中密度表格
                density_factor = 1.0
            else:  # 低密度表格
                density_factor = 1.2
        else:
            density_factor = 1.0
            
        # 综合调整
        adjusted_threshold = base_threshold * quality_factor * density_factor
        
        # 限制在合理范围内，但允许更大的阈值
        return max(0.5, min(20.0, adjusted_threshold))
    
    def detect_overlapping_points(self) -> List[List[Dict]]:
        """
        检测重叠或极近的角点
        
        Returns:
            重叠角点组列表
        """
        all_points = []
        
        # 收集所有角点
        for cell_idx, cell in enumerate(self.cells):
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                point = bbox[point_name]
                all_points.append({
                    'coords': point,
                    'cell_idx': cell_idx,
                    'point_name': point_name
                })
        
        # 检测重叠点组
        overlap_groups = []
        used_indices = set()
        
        for i, point1 in enumerate(all_points):
            if i in used_indices:
                continue
                
            group = [point1]
            used_indices.add(i)
            
            for j, point2 in enumerate(all_points):
                if j in used_indices:
                    continue
                    
                # 计算距离
                dist = math.sqrt(
                    (point1['coords'][0] - point2['coords'][0])**2 +
                    (point1['coords'][1] - point2['coords'][1])**2
                )
                
                if dist <= self.overlap_threshold:
                    group.append(point2)
                    used_indices.add(j)
            
            if len(group) > 1:
                overlap_groups.append(group)
                
        return overlap_groups
    
    def resolve_overlapping_points(self):
        """
        解决重叠角点问题
        """
        overlap_groups = self.detect_overlapping_points()
        
        if not overlap_groups:
            return
            
        print(f"  发现 {len(overlap_groups)} 组重叠角点")
        
        resolved_count = 0
        for group in overlap_groups:
            if len(group) < 2:
                continue
                
            # 计算组内角点的加权中心
            center_x = sum(p['coords'][0] for p in group) / len(group)
            center_y = sum(p['coords'][1] for p in group) / len(group)
            
            # 更新所有重叠点到中心位置
            for point in group:
                cell = self.cells[point['cell_idx']]
                cell['bbox'][point['point_name']][0] = center_x
                cell['bbox'][point['point_name']][1] = center_y
                resolved_count += 1
        
        print(f"  解决了 {resolved_count} 个重叠角点")
    
    def calculate_cell_regularity(self, cell: Dict) -> float:
        """
        计算单元格的规整度
        
        Args:
            cell: 单元格数据
            
        Returns:
            规整度分数 (0-1)
        """
        bbox = cell.get('bbox', {})
        if not all(p in bbox for p in ['p1', 'p2', 'p3', 'p4']):
            return 0.0
            
        # 获取四个角点
        p1, p2, p3, p4 = [bbox[p] for p in ['p1', 'p2', 'p3', 'p4']]
        
        # 计算四条边的长度
        side1 = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
        side2 = math.sqrt((p3[0] - p2[0])**2 + (p3[1] - p2[1])**2)
        side3 = math.sqrt((p4[0] - p3[0])**2 + (p4[1] - p3[1])**2)
        side4 = math.sqrt((p1[0] - p4[0])**2 + (p1[1] - p4[1])**2)
        
        # 对边长度比
        if side1 > 0 and side3 > 0:
            ratio1 = min(side1, side3) / max(side1, side3)
        else:
            ratio1 = 0
            
        if side2 > 0 and side4 > 0:
            ratio2 = min(side2, side4) / max(side2, side4)
        else:
            ratio2 = 0
        
        return (ratio1 + ratio2) / 2
    
    def quality_aware_fine_tuning(self):
        """
        基于质量样本特征的精细调整
        """
        print("执行质量感知微调...")
        
        # 1. 首先解决重叠角点
        self.resolve_overlapping_points()
        
        # 2. 基于规整度进行分组调整
        high_regularity_cells = []
        low_regularity_cells = []
        
        for cell in self.cells:
            regularity = self.calculate_cell_regularity(cell)
            if regularity >= self.regularity_threshold:
                high_regularity_cells.append(cell)
            else:
                low_regularity_cells.append(cell)
        
        print(f"  高规整度单元格: {len(high_regularity_cells)}")
        print(f"  低规整度单元格: {len(low_regularity_cells)}")
        
        # 3. 对高规整度单元格使用更精确的对齐
        if high_regularity_cells:
            self._fine_tune_regular_cells(high_regularity_cells)
            
        # 4. 对低规整度单元格使用更宽松的对齐
        if low_regularity_cells:
            self._fine_tune_irregular_cells(low_regularity_cells)
    
    def _fine_tune_regular_cells(self, cells: List[Dict]):
        """对规整单元格进行精确微调"""
        # 使用更小的阈值
        precise_threshold = self.tolerance * 0.3
        
        all_points = []
        for cell in cells:
            cell_idx = self.cells.index(cell)
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                point = bbox[point_name]
                all_points.append({
                    'coords': point,
                    'cell_idx': cell_idx,
                    'point_name': point_name
                })
        
        aligned_count = self._fine_tune_with_threshold(all_points, precise_threshold, "精确")
        print(f"  精确微调了 {aligned_count} 个规整单元格角点")
    
    def _fine_tune_irregular_cells(self, cells: List[Dict]):
        """对不规整单元格进行宽松微调"""
        # 使用更大的阈值
        loose_threshold = self.tolerance * 0.8
        
        all_points = []
        for cell in cells:
            cell_idx = self.cells.index(cell)
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                point = bbox[point_name]
                all_points.append({
                    'coords': point,
                    'cell_idx': cell_idx,
                    'point_name': point_name
                })
        
        aligned_count = self._fine_tune_with_threshold(all_points, loose_threshold, "宽松")
        print(f"  宽松微调了 {aligned_count} 个不规整单元格角点")
    
    def optimize_alignment(self):
        """
        执行质量感知的角点对齐优化
        """
        print("构建网格结构...")
        self.build_grid_structure()

        print("对齐行边界...")
        self.align_row_boundaries()

        print("对齐列边界...")
        self.align_column_boundaries()

        print("质量感知微调...")
        self.quality_aware_fine_tuning()

        print("质量感知优化完成！")
        
        # 评估优化质量
        self._evaluate_optimization_quality()


def test_quality_aware_optimizer():
    """测试质量感知优化器"""
    print("🔧 测试质量感知优化器")
    print("=" * 60)
    
    # 选择几个合格样本进行测试
    test_files = [
        "115919662_table_annotation.json",  # 高规整度，低对齐误差
        "1wizqck4_table_annotation.json",   # 大表格，多单元格
        "_cevaua3_table_annotation.json",   # 完美对齐样本
    ]
    
    for test_file in test_files:
        file_path = Path("borderless_table") / test_file
        if not file_path.exists():
            print(f"❌ 测试文件不存在: {test_file}")
            continue
            
        print(f"\n测试文件: {test_file}")
        print("-" * 40)
        
        # 查找对应图片
        image_file = None
        for ext in ['.jpg', '.jpeg', '.png']:
            img_path = file_path.parent / f"{file_path.stem.replace('_table_annotation', '')}{ext}"
            if img_path.exists():
                image_file = str(img_path)
                break
        
        # 创建质量感知优化器
        optimizer = QualityAwareOptimizer(
            tolerance=2.0,  # 基于分析结果调整
            preserve_perspective=True,
            adaptive_threshold=True
        )
        
        # 执行优化
        output_file = f"quality_optimized_{test_file}"
        result = optimizer.optimize_table_annotation(
            str(file_path),
            output_file,
            image_file
        )
        
        if result['success']:
            print(f"✅ 优化成功")
            print(f"📊 单元格数: {result['cell_count']}")
            if result.get('adaptive_threshold'):
                print(f"🎯 自适应阈值: {result['adaptive_threshold']:.2f}px")
        else:
            print(f"❌ 优化失败: {result['error']}")


if __name__ == "__main__":
    test_quality_aware_optimizer()
