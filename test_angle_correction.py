#!/usr/bin/env python3
"""
测试角度校正功能

专门测试解决图片旋转导致的表格线角度分叉问题
"""

import os
import json
import math
import time
from pathlib import Path
from table_annotation_optimizer import PerspectiveAwareOptimizer


def calculate_angle_variance(cells):
    """
    计算表格线角度的方差，用于评估角度一致性
    
    Args:
        cells: 单元格列表
        
    Returns:
        角度方差统计
    """
    horizontal_angles = []
    vertical_angles = []
    
    for cell in cells:
        bbox = cell.get('bbox', {})
        if not all(p in bbox for p in ['p1', 'p2', 'p3', 'p4']):
            continue
            
        # 计算四条边的角度
        edges = [
            (bbox['p1'], bbox['p2']),  # 上边
            (bbox['p2'], bbox['p3']),  # 右边
            (bbox['p3'], bbox['p4']),  # 下边
            (bbox['p4'], bbox['p1'])   # 左边
        ]
        
        for i, (p1, p2) in enumerate(edges):
            dx = p2[0] - p1[0]
            dy = p2[1] - p1[1]
            
            if abs(dx) < 1e-6:  # 垂直线
                angle = 90.0
            else:
                angle = math.atan2(dy, dx) * 180 / math.pi
            
            # 标准化角度到0-180度
            if angle < 0:
                angle += 180
                
            # 分类为水平或垂直线
            if i % 2 == 0:  # 上边和下边（水平线）
                horizontal_angles.append(angle)
            else:  # 左边和右边（垂直线）
                vertical_angles.append(angle)
    
    # 计算方差
    h_variance = 0
    v_variance = 0
    
    if horizontal_angles:
        h_mean = sum(horizontal_angles) / len(horizontal_angles)
        h_variance = sum((a - h_mean)**2 for a in horizontal_angles) / len(horizontal_angles)
    
    if vertical_angles:
        v_mean = sum(vertical_angles) / len(vertical_angles)
        v_variance = sum((a - v_mean)**2 for a in vertical_angles) / len(vertical_angles)
    
    return {
        'horizontal_variance': h_variance,
        'vertical_variance': v_variance,
        'horizontal_angles': horizontal_angles,
        'vertical_angles': vertical_angles,
        'total_variance': h_variance + v_variance
    }


def analyze_angle_consistency(annotation_file):
    """
    分析标注文件的角度一致性
    
    Args:
        annotation_file: 标注文件路径
        
    Returns:
        角度一致性分析结果
    """
    try:
        with open(annotation_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        cells = data.get('cells', [])
        if not cells:
            return None
            
        angle_stats = calculate_angle_variance(cells)
        
        return {
            'file': annotation_file,
            'cell_count': len(cells),
            'horizontal_variance': angle_stats['horizontal_variance'],
            'vertical_variance': angle_stats['vertical_variance'],
            'total_variance': angle_stats['total_variance'],
            'angle_consistency_score': 1.0 / (1.0 + angle_stats['total_variance'])  # 越接近1越好
        }
        
    except Exception as e:
        print(f"分析文件失败 {annotation_file}: {e}")
        return None


def test_angle_correction_on_sample(sample_file, image_file=None):
    """
    在单个样本上测试角度校正
    
    Args:
        sample_file: 样本文件路径
        image_file: 对应图片文件路径
        
    Returns:
        测试结果
    """
    print(f"\n测试样本: {Path(sample_file).name}")
    print("-" * 50)
    
    # 分析原始角度一致性
    print("📊 原始角度一致性分析:")
    original_stats = analyze_angle_consistency(sample_file)
    if original_stats:
        print(f"  单元格数: {original_stats['cell_count']}")
        print(f"  水平线角度方差: {original_stats['horizontal_variance']:.2f}")
        print(f"  垂直线角度方差: {original_stats['vertical_variance']:.2f}")
        print(f"  总角度方差: {original_stats['total_variance']:.2f}")
        print(f"  角度一致性评分: {original_stats['angle_consistency_score']:.3f}")
    
    # 测试不启用角度校正
    print("\n🔧 传统算法（不启用角度校正）:")
    start_time = time.time()
    
    traditional_optimizer = PerspectiveAwareOptimizer(
        tolerance=2.0,
        preserve_perspective=True,
        adaptive_threshold=True,
        quality_aware=True,
        angle_correction=False  # 不启用角度校正
    )
    
    traditional_output = f"traditional_{Path(sample_file).name}"
    traditional_result = traditional_optimizer.optimize_table_annotation(
        sample_file,
        traditional_output,
        image_file
    )
    
    traditional_time = time.time() - start_time
    
    if traditional_result['success']:
        print(f"  ✅ 处理成功，耗时: {traditional_time:.3f}秒")
        traditional_stats = analyze_angle_consistency(traditional_output)
        if traditional_stats:
            print(f"  角度一致性评分: {traditional_stats['angle_consistency_score']:.3f}")
    else:
        print(f"  ❌ 处理失败: {traditional_result['error']}")
        traditional_stats = None
    
    # 测试启用角度校正
    print("\n🎯 角度校正算法:")
    start_time = time.time()
    
    angle_optimizer = PerspectiveAwareOptimizer(
        tolerance=2.0,
        preserve_perspective=True,
        adaptive_threshold=True,
        quality_aware=True,
        angle_correction=True  # 启用角度校正
    )
    
    angle_output = f"angle_corrected_{Path(sample_file).name}"
    angle_result = angle_optimizer.optimize_table_annotation(
        sample_file,
        angle_output,
        image_file
    )
    
    angle_time = time.time() - start_time
    
    if angle_result['success']:
        print(f"  ✅ 处理成功，耗时: {angle_time:.3f}秒")
        angle_stats = analyze_angle_consistency(angle_output)
        if angle_stats:
            print(f"  角度一致性评分: {angle_stats['angle_consistency_score']:.3f}")
    else:
        print(f"  ❌ 处理失败: {angle_result['error']}")
        angle_stats = None
    
    # 对比结果
    print("\n📈 对比结果:")
    if original_stats and traditional_stats and angle_stats:
        print(f"  原始 -> 传统算法: {original_stats['angle_consistency_score']:.3f} -> {traditional_stats['angle_consistency_score']:.3f}")
        print(f"  原始 -> 角度校正: {original_stats['angle_consistency_score']:.3f} -> {angle_stats['angle_consistency_score']:.3f}")
        
        traditional_improvement = traditional_stats['angle_consistency_score'] - original_stats['angle_consistency_score']
        angle_improvement = angle_stats['angle_consistency_score'] - original_stats['angle_consistency_score']
        
        print(f"  传统算法改进: {traditional_improvement:+.3f}")
        print(f"  角度校正改进: {angle_improvement:+.3f}")
        
        if angle_improvement > traditional_improvement:
            print("  ✅ 角度校正算法效果更好")
        elif angle_improvement == traditional_improvement:
            print("  ➡️  两种算法效果相同")
        else:
            print("  ⚠️  传统算法效果更好")
    
    # 清理测试文件
    for test_file in [traditional_output, angle_output]:
        try:
            if os.path.exists(test_file):
                os.remove(test_file)
        except:
            pass
    
    return {
        'sample': Path(sample_file).name,
        'original_stats': original_stats,
        'traditional_stats': traditional_stats,
        'angle_stats': angle_stats,
        'traditional_time': traditional_time if traditional_result['success'] else None,
        'angle_time': angle_time if angle_result['success'] else None
    }


def test_angle_correction():
    """测试角度校正功能"""
    print("🎯 角度校正功能测试")
    print("=" * 60)
    print("专门解决图片旋转导致的表格线小角度分叉问题")
    print("=" * 60)
    
    # 查找测试文件
    test_dirs = [
        "borderless_table",
        "organized_dataset",
        "F:\\workspace\\datasets\\Relabel_TabRecSet\\chinese\\curved_tables"
    ]
    
    test_files = []
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            # 优先选择可能有旋转问题的文件
            json_files = list(Path(test_dir).glob("*.json"))
            test_files.extend(json_files[:5])  # 取前5个文件测试
            break
    
    if not test_files:
        print("❌ 未找到测试文件")
        return
    
    print(f"找到 {len(test_files)} 个测试文件")
    
    results = []
    
    for test_file in test_files:
        # 查找对应图片
        image_file = None
        for ext in ['.jpg', '.jpeg', '.png']:
            img_path = test_file.parent / f"{test_file.stem.replace('_table_annotation', '')}{ext}"
            if img_path.exists():
                image_file = str(img_path)
                break
        
        # 执行测试
        result = test_angle_correction_on_sample(str(test_file), image_file)
        if result:
            results.append(result)
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 角度校正测试总结")
    print("=" * 60)
    
    if not results:
        print("❌ 没有成功的测试结果")
        return
    
    # 统计改进效果
    traditional_improvements = []
    angle_improvements = []
    
    for result in results:
        if (result['original_stats'] and result['traditional_stats'] and 
            result['angle_stats']):
            
            original_score = result['original_stats']['angle_consistency_score']
            traditional_score = result['traditional_stats']['angle_consistency_score']
            angle_score = result['angle_stats']['angle_consistency_score']
            
            traditional_improvements.append(traditional_score - original_score)
            angle_improvements.append(angle_score - original_score)
    
    if traditional_improvements and angle_improvements:
        avg_traditional = sum(traditional_improvements) / len(traditional_improvements)
        avg_angle = sum(angle_improvements) / len(angle_improvements)
        
        print(f"成功测试样本数: {len(results)}")
        print(f"传统算法平均改进: {avg_traditional:+.3f}")
        print(f"角度校正平均改进: {avg_angle:+.3f}")
        
        if avg_angle > avg_traditional:
            print("✅ 角度校正算法整体效果更好")
        else:
            print("⚠️  需要进一步优化角度校正算法")
    
    print(f"\n🎯 角度校正功能特点:")
    print("1. 检测图片旋转角度")
    print("2. 识别共享角点")
    print("3. 校正从同一点分出的线条角度")
    print("4. 减少小角度分叉问题")
    print("5. 保持表格线的平行和垂直关系")


def main():
    """主函数"""
    print("🧪 角度校正功能测试")
    print("=" * 60)
    
    test_angle_correction()
    
    print(f"\n🎉 测试完成！")
    print("角度校正功能专门解决:")
    print("- 图片旋转导致的角度偏差")
    print("- 从同一坐标点分出的表格线角度分叉")
    print("- 表格线角度不一致问题")
    
    return 0


if __name__ == "__main__":
    exit(main())
