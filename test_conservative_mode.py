#!/usr/bin/env python3
"""
测试保守模式优化器

专门测试减少标注改动的保守优化算法
"""

import os
import json
import math
import time
from pathlib import Path
from table_annotation_optimizer import PerspectiveAwareOptimizer


def calculate_annotation_change(original_cells, optimized_cells):
    """
    计算标注改动程度
    
    Args:
        original_cells: 原始单元格
        optimized_cells: 优化后单元格
        
    Returns:
        改动统计
    """
    if len(original_cells) != len(optimized_cells):
        return {'error': '单元格数量不匹配'}
    
    total_distance = 0
    max_distance = 0
    changed_points = 0
    total_points = 0
    
    for orig_cell, opt_cell in zip(original_cells, optimized_cells):
        orig_bbox = orig_cell.get('bbox', {})
        opt_bbox = opt_cell.get('bbox', {})
        
        for point_name in ['p1', 'p2', 'p3', 'p4']:
            if point_name in orig_bbox and point_name in opt_bbox:
                orig_point = orig_bbox[point_name]
                opt_point = opt_bbox[point_name]
                
                # 计算点的移动距离
                distance = math.sqrt((opt_point[0] - orig_point[0])**2 + 
                                   (opt_point[1] - orig_point[1])**2)
                
                total_distance += distance
                max_distance = max(max_distance, distance)
                total_points += 1
                
                if distance > 0.1:  # 认为移动超过0.1像素为有效改动
                    changed_points += 1
    
    return {
        'total_points': total_points,
        'changed_points': changed_points,
        'change_ratio': changed_points / total_points if total_points > 0 else 0,
        'avg_distance': total_distance / total_points if total_points > 0 else 0,
        'max_distance': max_distance,
        'total_distance': total_distance
    }


def test_conservative_vs_aggressive():
    """对比保守模式和激进模式"""
    print("🔧 保守模式 vs 激进模式对比测试")
    print("=" * 60)
    print("测试目标：减少标注改动，保持原有精度")
    print("=" * 60)
    
    # 选择测试文件
    test_files = list(Path("borderless_table").glob("*.json"))[:3]
    
    if not test_files:
        print("❌ 未找到测试文件")
        return
    
    results = []
    
    for test_file in test_files:
        print(f"\n测试样本: {test_file.name}")
        print("-" * 50)
        
        # 读取原始数据
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
            original_cells = original_data.get('cells', [])
            
            if not original_cells:
                print("  ❌ 无单元格数据")
                continue
                
            print(f"  原始单元格数: {len(original_cells)}")
            
        except Exception as e:
            print(f"  ❌ 读取失败: {e}")
            continue
        
        # 查找对应图片
        image_file = None
        for ext in ['.jpg', '.jpeg', '.png']:
            img_path = test_file.parent / f"{test_file.stem.replace('_table_annotation', '')}{ext}"
            if img_path.exists():
                image_file = str(img_path)
                break
        
        # 测试保守模式
        print("\n🛡️  保守模式:")
        start_time = time.time()
        
        conservative_optimizer = PerspectiveAwareOptimizer(
            tolerance=1.5,              # 小阈值
            preserve_perspective=True,
            adaptive_threshold=False,   # 关闭自适应
            quality_aware=False,        # 关闭质量感知
            angle_correction=False,     # 关闭角度校正
            conservative_mode=True      # 启用保守模式
        )
        
        conservative_output = f"conservative_{test_file.name}"
        conservative_result = conservative_optimizer.optimize_table_annotation(
            str(test_file),
            conservative_output,
            image_file
        )
        
        conservative_time = time.time() - start_time
        
        if conservative_result['success']:
            print(f"  ✅ 处理成功，耗时: {conservative_time:.3f}秒")
            
            # 分析改动程度
            try:
                with open(conservative_output, 'r', encoding='utf-8') as f:
                    conservative_data = json.load(f)
                conservative_cells = conservative_data.get('cells', [])
                
                conservative_change = calculate_annotation_change(original_cells, conservative_cells)
                print(f"  📊 改动统计:")
                print(f"     改动点数: {conservative_change['changed_points']}/{conservative_change['total_points']}")
                print(f"     改动比例: {conservative_change['change_ratio']:.1%}")
                print(f"     平均移动: {conservative_change['avg_distance']:.2f}px")
                print(f"     最大移动: {conservative_change['max_distance']:.2f}px")
                
            except Exception as e:
                print(f"  ⚠️  分析失败: {e}")
                conservative_change = None
        else:
            print(f"  ❌ 处理失败: {conservative_result['error']}")
            conservative_change = None
        
        # 测试激进模式（原有算法）
        print("\n⚡ 激进模式:")
        start_time = time.time()
        
        aggressive_optimizer = PerspectiveAwareOptimizer(
            tolerance=2.0,              # 大阈值
            preserve_perspective=True,
            adaptive_threshold=True,    # 启用自适应
            quality_aware=True,         # 启用质量感知
            angle_correction=True,      # 启用角度校正
            conservative_mode=False     # 关闭保守模式
        )
        
        aggressive_output = f"aggressive_{test_file.name}"
        aggressive_result = aggressive_optimizer.optimize_table_annotation(
            str(test_file),
            aggressive_output,
            image_file
        )
        
        aggressive_time = time.time() - start_time
        
        if aggressive_result['success']:
            print(f"  ✅ 处理成功，耗时: {aggressive_time:.3f}秒")
            
            # 分析改动程度
            try:
                with open(aggressive_output, 'r', encoding='utf-8') as f:
                    aggressive_data = json.load(f)
                aggressive_cells = aggressive_data.get('cells', [])
                
                aggressive_change = calculate_annotation_change(original_cells, aggressive_cells)
                print(f"  📊 改动统计:")
                print(f"     改动点数: {aggressive_change['changed_points']}/{aggressive_change['total_points']}")
                print(f"     改动比例: {aggressive_change['change_ratio']:.1%}")
                print(f"     平均移动: {aggressive_change['avg_distance']:.2f}px")
                print(f"     最大移动: {aggressive_change['max_distance']:.2f}px")
                
            except Exception as e:
                print(f"  ⚠️  分析失败: {e}")
                aggressive_change = None
        else:
            print(f"  ❌ 处理失败: {aggressive_result['error']}")
            aggressive_change = None
        
        # 对比结果
        print("\n📈 改动对比:")
        if conservative_change and aggressive_change:
            print(f"  保守模式改动比例: {conservative_change['change_ratio']:.1%}")
            print(f"  激进模式改动比例: {aggressive_change['change_ratio']:.1%}")
            print(f"  保守模式平均移动: {conservative_change['avg_distance']:.2f}px")
            print(f"  激进模式平均移动: {aggressive_change['avg_distance']:.2f}px")
            
            if conservative_change['change_ratio'] < aggressive_change['change_ratio']:
                reduction = (aggressive_change['change_ratio'] - conservative_change['change_ratio']) * 100
                print(f"  ✅ 保守模式减少了 {reduction:.1f}% 的改动")
            else:
                print(f"  ⚠️  保守模式改动仍然较大")
        
        # 记录结果
        results.append({
            'sample': test_file.name,
            'conservative_change': conservative_change,
            'aggressive_change': aggressive_change,
            'conservative_time': conservative_time if conservative_result['success'] else None,
            'aggressive_time': aggressive_time if aggressive_result['success'] else None
        })
        
        # 清理测试文件
        for test_output in [conservative_output, aggressive_output]:
            try:
                if os.path.exists(test_output):
                    os.remove(test_output)
            except:
                pass
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 保守模式测试总结")
    print("=" * 60)
    
    if not results:
        print("❌ 没有成功的测试结果")
        return
    
    # 统计改动减少效果
    valid_results = [r for r in results if r['conservative_change'] and r['aggressive_change']]
    
    if valid_results:
        conservative_avg_change = sum(r['conservative_change']['change_ratio'] for r in valid_results) / len(valid_results)
        aggressive_avg_change = sum(r['aggressive_change']['change_ratio'] for r in valid_results) / len(valid_results)
        
        conservative_avg_distance = sum(r['conservative_change']['avg_distance'] for r in valid_results) / len(valid_results)
        aggressive_avg_distance = sum(r['aggressive_change']['avg_distance'] for r in valid_results) / len(valid_results)
        
        print(f"成功测试样本数: {len(valid_results)}")
        print(f"保守模式平均改动比例: {conservative_avg_change:.1%}")
        print(f"激进模式平均改动比例: {aggressive_avg_change:.1%}")
        print(f"保守模式平均移动距离: {conservative_avg_distance:.2f}px")
        print(f"激进模式平均移动距离: {aggressive_avg_distance:.2f}px")
        
        change_reduction = (aggressive_avg_change - conservative_avg_change) * 100
        distance_reduction = aggressive_avg_distance - conservative_avg_distance
        
        if change_reduction > 0:
            print(f"✅ 保守模式减少了 {change_reduction:.1f}% 的改动比例")
        if distance_reduction > 0:
            print(f"✅ 保守模式减少了 {distance_reduction:.2f}px 的平均移动距离")
    
    print(f"\n🛡️  保守模式特点:")
    print("1. 更小的容差阈值（1.5px vs 2.0px）")
    print("2. 关闭自适应阈值，使用固定阈值")
    print("3. 关闭质量感知优化，避免过度调整")
    print("4. 关闭角度校正，避免过度修改")
    print("5. 调整限制机制，最大调整比例10%")
    print("6. 保守更新策略，过大调整时只调整50%")


def main():
    """主函数"""
    print("🧪 保守模式优化器测试")
    print("=" * 60)
    
    test_conservative_vs_aggressive()
    
    print(f"\n🎉 测试完成！")
    print("保守模式专门解决:")
    print("- 减少标注改动幅度")
    print("- 保持原有标注精度")
    print("- 避免过度优化")
    print("- 提供可控的微调")
    
    return 0


if __name__ == "__main__":
    exit(main())
