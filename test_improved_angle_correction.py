#!/usr/bin/env python3
"""
测试改进的角度校正功能

专门测试解决图片旋转导致的表格线小角度分叉问题
"""

import os
import json
import math
import time
from pathlib import Path
from table_annotation_optimizer import PerspectiveAwareOptimizer


def calculate_angle_bifurcation_score(cells):
    """
    计算角度分叉评分
    
    Args:
        cells: 单元格列表
        
    Returns:
        分叉评分（越低越好）
    """
    total_bifurcation = 0
    shared_points = {}
    
    # 收集共享点
    for cell_idx, cell in enumerate(cells):
        bbox = cell.get('bbox', {})
        for point_name in ['p1', 'p2', 'p3', 'p4']:
            if point_name in bbox:
                coords = bbox[point_name]
                coord_key = f"{coords[0]:.1f},{coords[1]:.1f}"
                
                if coord_key not in shared_points:
                    shared_points[coord_key] = []
                
                shared_points[coord_key].append({
                    'cell_idx': cell_idx,
                    'point_name': point_name,
                    'coords': coords
                })
    
    # 分析每个共享点的角度分叉
    for coord_key, point_info in shared_points.items():
        if len(point_info) < 2:
            continue
            
        # 收集从该点出发的线条角度
        horizontal_angles = []
        vertical_angles = []
        
        for info in point_info:
            cell = cells[info['cell_idx']]
            bbox = cell['bbox']
            point_name = info['point_name']
            coords = info['coords']
            
            # 获取连接的点
            connections = {
                'p1': [('p2', 'horizontal'), ('p4', 'vertical')],
                'p2': [('p1', 'horizontal'), ('p3', 'vertical')],
                'p3': [('p2', 'vertical'), ('p4', 'horizontal')],
                'p4': [('p3', 'horizontal'), ('p1', 'vertical')]
            }
            
            for connected_point, direction in connections.get(point_name, []):
                if connected_point in bbox:
                    target_coords = bbox[connected_point]
                    dx = target_coords[0] - coords[0]
                    dy = target_coords[1] - coords[1]
                    
                    if abs(dx) < 1e-6:
                        angle = 90.0
                    else:
                        angle = math.atan2(dy, dx) * 180 / math.pi
                    
                    if direction == 'horizontal':
                        horizontal_angles.append(angle)
                    else:
                        vertical_angles.append(angle)
        
        # 计算角度分叉程度
        if len(horizontal_angles) > 1:
            h_variance = sum((a - sum(horizontal_angles)/len(horizontal_angles))**2 
                           for a in horizontal_angles) / len(horizontal_angles)
            total_bifurcation += h_variance
        
        if len(vertical_angles) > 1:
            v_variance = sum((a - sum(vertical_angles)/len(vertical_angles))**2 
                           for a in vertical_angles) / len(vertical_angles)
            total_bifurcation += v_variance
    
    return total_bifurcation


def test_angle_bifurcation_correction():
    """测试角度分叉校正"""
    print("🎯 改进的角度分叉校正测试")
    print("=" * 60)
    print("专门解决从同一坐标点分出的表格线小角度分叉问题")
    print("=" * 60)
    
    # 查找测试文件
    test_files = list(Path("borderless_table").glob("*.json"))[:5]
    
    if not test_files:
        print("❌ 未找到测试文件")
        return
    
    print(f"找到 {len(test_files)} 个测试文件")
    
    results = []
    
    for test_file in test_files:
        print(f"\n测试样本: {test_file.name}")
        print("-" * 50)
        
        # 查找对应图片
        image_file = None
        for ext in ['.jpg', '.jpeg', '.png']:
            img_path = test_file.parent / f"{test_file.stem.replace('_table_annotation', '')}{ext}"
            if img_path.exists():
                image_file = str(img_path)
                break
        
        # 分析原始角度分叉
        print("📊 原始角度分叉分析:")
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
            
            original_cells = original_data.get('cells', [])
            if not original_cells:
                print("  ❌ 无单元格数据")
                continue
                
            original_bifurcation = calculate_angle_bifurcation_score(original_cells)
            print(f"  单元格数: {len(original_cells)}")
            print(f"  角度分叉评分: {original_bifurcation:.2f}")
            
        except Exception as e:
            print(f"  ❌ 分析失败: {e}")
            continue
        
        # 测试传统算法（不启用角度校正）
        print("\n🔧 传统算法（不启用角度校正）:")
        start_time = time.time()
        
        traditional_optimizer = PerspectiveAwareOptimizer(
            tolerance=2.0,
            preserve_perspective=True,
            adaptive_threshold=True,
            quality_aware=True,
            angle_correction=False  # 不启用角度校正
        )
        
        traditional_output = f"traditional_{test_file.name}"
        traditional_result = traditional_optimizer.optimize_table_annotation(
            str(test_file),
            traditional_output,
            image_file
        )
        
        traditional_time = time.time() - start_time
        
        if traditional_result['success']:
            print(f"  ✅ 处理成功，耗时: {traditional_time:.3f}秒")
            
            # 分析传统算法的角度分叉
            try:
                with open(traditional_output, 'r', encoding='utf-8') as f:
                    traditional_data = json.load(f)
                traditional_bifurcation = calculate_angle_bifurcation_score(traditional_data['cells'])
                print(f"  角度分叉评分: {traditional_bifurcation:.2f}")
            except Exception as e:
                print(f"  ⚠️  分析失败: {e}")
                traditional_bifurcation = float('inf')
        else:
            print(f"  ❌ 处理失败: {traditional_result['error']}")
            traditional_bifurcation = float('inf')
        
        # 测试改进的角度校正算法
        print("\n🎯 改进的角度校正算法:")
        start_time = time.time()
        
        angle_optimizer = PerspectiveAwareOptimizer(
            tolerance=2.0,
            preserve_perspective=True,
            adaptive_threshold=True,
            quality_aware=True,
            angle_correction=True  # 启用改进的角度校正
        )
        
        angle_output = f"improved_angle_{test_file.name}"
        angle_result = angle_optimizer.optimize_table_annotation(
            str(test_file),
            angle_output,
            image_file
        )
        
        angle_time = time.time() - start_time
        
        if angle_result['success']:
            print(f"  ✅ 处理成功，耗时: {angle_time:.3f}秒")
            
            # 分析角度校正算法的角度分叉
            try:
                with open(angle_output, 'r', encoding='utf-8') as f:
                    angle_data = json.load(f)
                angle_bifurcation = calculate_angle_bifurcation_score(angle_data['cells'])
                print(f"  角度分叉评分: {angle_bifurcation:.2f}")
            except Exception as e:
                print(f"  ⚠️  分析失败: {e}")
                angle_bifurcation = float('inf')
        else:
            print(f"  ❌ 处理失败: {angle_result['error']}")
            angle_bifurcation = float('inf')
        
        # 对比结果
        print("\n📈 角度分叉改进对比:")
        if original_bifurcation < float('inf'):
            traditional_improvement = original_bifurcation - traditional_bifurcation
            angle_improvement = original_bifurcation - angle_bifurcation
            
            print(f"  原始分叉评分: {original_bifurcation:.2f}")
            print(f"  传统算法改进: {traditional_improvement:+.2f}")
            print(f"  角度校正改进: {angle_improvement:+.2f}")
            
            if angle_improvement > traditional_improvement:
                print("  ✅ 角度校正算法显著减少了角度分叉")
            elif angle_improvement > 0:
                print("  ✅ 角度校正算法有效减少了角度分叉")
            else:
                print("  ⚠️  角度校正算法需要进一步优化")
        
        # 记录结果
        results.append({
            'sample': test_file.name,
            'original_bifurcation': original_bifurcation,
            'traditional_bifurcation': traditional_bifurcation,
            'angle_bifurcation': angle_bifurcation,
            'traditional_time': traditional_time if traditional_result['success'] else None,
            'angle_time': angle_time if angle_result['success'] else None
        })
        
        # 清理测试文件
        for test_output in [traditional_output, angle_output]:
            try:
                if os.path.exists(test_output):
                    os.remove(test_output)
            except:
                pass
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 改进的角度校正测试总结")
    print("=" * 60)
    
    if not results:
        print("❌ 没有成功的测试结果")
        return
    
    # 统计改进效果
    valid_results = [r for r in results if r['original_bifurcation'] < float('inf')]
    
    if valid_results:
        traditional_improvements = []
        angle_improvements = []
        
        for result in valid_results:
            if (result['traditional_bifurcation'] < float('inf') and 
                result['angle_bifurcation'] < float('inf')):
                
                original_score = result['original_bifurcation']
                traditional_score = result['traditional_bifurcation']
                angle_score = result['angle_bifurcation']
                
                traditional_improvements.append(original_score - traditional_score)
                angle_improvements.append(original_score - angle_score)
        
        if traditional_improvements and angle_improvements:
            avg_traditional = sum(traditional_improvements) / len(traditional_improvements)
            avg_angle = sum(angle_improvements) / len(angle_improvements)
            
            print(f"成功测试样本数: {len(valid_results)}")
            print(f"传统算法平均改进: {avg_traditional:+.2f}")
            print(f"角度校正平均改进: {avg_angle:+.2f}")
            
            if avg_angle > avg_traditional * 1.2:
                print("✅ 改进的角度校正算法显著优于传统算法")
            elif avg_angle > avg_traditional:
                print("✅ 改进的角度校正算法优于传统算法")
            else:
                print("⚠️  需要进一步优化角度校正算法")
    
    print(f"\n🎯 改进的角度校正功能特点:")
    print("1. 智能检测角度分叉点")
    print("2. 分析水平和垂直线的角度差异")
    print("3. 使用角度吸附到标准角度（0°, 90°, 180°, -90°）")
    print("4. 全局角度标准化处理")
    print("5. 专门减少从同一点分出的线条角度分叉")


def main():
    """主函数"""
    print("🧪 改进的角度校正功能测试")
    print("=" * 60)
    
    test_angle_bifurcation_correction()
    
    print(f"\n🎉 测试完成！")
    print("改进的角度校正专门解决:")
    print("- 图片旋转导致的角度偏差")
    print("- 从同一坐标点分出的表格线小角度分叉")
    print("- 表格线角度不一致问题")
    print("- 通过角度吸附确保线条平行和垂直")
    
    return 0


if __name__ == "__main__":
    exit(main())
