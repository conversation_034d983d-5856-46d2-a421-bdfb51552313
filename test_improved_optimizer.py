#!/usr/bin/env python3
"""
测试改进后的表格标注优化器

主要测试：
1. 属性保护功能
2. 改进的算法性能
3. 质量评估功能
"""

import os
import json
import time
from pathlib import Path
from table_annotation_optimizer import PerspectiveAwareOptimizer


def test_attribute_preservation():
    """测试属性保护功能"""
    print("=" * 60)
    print("测试属性保护功能")
    print("=" * 60)
    
    # 查找测试文件
    test_files = list(Path("organized_dataset").glob("*_table_annotation.json"))[:3]
    
    if not test_files:
        print("❌ 未找到测试文件")
        return False
    
    success_count = 0
    
    for test_file in test_files:
        print(f"\n测试文件: {test_file.name}")
        
        try:
            # 读取原始文件
            with open(test_file, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
            
            # 创建优化器并处理
            optimizer = PerspectiveAwareOptimizer(
                tolerance=3.0,
                preserve_perspective=True,
                adaptive_threshold=True
            )
            
            # 查找对应图片
            image_file = None
            for ext in ['.jpg', '.jpeg', '.png']:
                img_path = test_file.parent / f"{test_file.stem.replace('_table_annotation', '')}{ext}"
                if img_path.exists():
                    image_file = str(img_path)
                    break
            
            # 执行优化
            output_file = f"test_output_{test_file.name}"
            result = optimizer.optimize_table_annotation(
                str(test_file), 
                output_file, 
                image_file
            )
            
            if result['success']:
                # 读取输出文件
                with open(output_file, 'r', encoding='utf-8') as f:
                    output_data = json.load(f)
                
                # 检查属性保护
                preserved_attrs = []
                
                # 检查顶层属性
                for key in original_data.keys():
                    if key != 'cells':  # cells会被修改
                        if key in output_data and output_data[key] == original_data[key]:
                            preserved_attrs.append(key)
                
                print(f"  ✅ 成功处理，保护了属性: {preserved_attrs}")
                
                # 检查单元格属性
                if len(original_data['cells']) == len(output_data['cells']):
                    cell_attrs_preserved = True
                    for i, (orig_cell, out_cell) in enumerate(zip(original_data['cells'], output_data['cells'])):
                        for key in orig_cell.keys():
                            if key != 'bbox':  # bbox会被修改
                                if key not in out_cell or out_cell[key] != orig_cell[key]:
                                    cell_attrs_preserved = False
                                    break
                        if not cell_attrs_preserved:
                            break
                    
                    if cell_attrs_preserved:
                        print(f"  ✅ 单元格属性保护正确")
                    else:
                        print(f"  ⚠️  单元格属性可能有变化")
                
                success_count += 1
                
                # 清理测试文件
                os.remove(output_file)
                
            else:
                print(f"  ❌ 处理失败: {result['error']}")
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
    
    print(f"\n属性保护测试结果: {success_count}/{len(test_files)} 成功")
    return success_count == len(test_files)


def test_algorithm_performance():
    """测试算法性能改进"""
    print("\n" + "=" * 60)
    print("测试算法性能改进")
    print("=" * 60)
    
    # 选择几个测试文件
    test_files = list(Path("organized_dataset").glob("*_table_annotation.json"))[:5]
    
    if not test_files:
        print("❌ 未找到测试文件")
        return False
    
    results = []
    
    for test_file in test_files:
        print(f"\n测试文件: {test_file.name}")
        
        try:
            # 查找对应图片
            image_file = None
            for ext in ['.jpg', '.jpeg', '.png']:
                img_path = test_file.parent / f"{test_file.stem.replace('_table_annotation', '')}{ext}"
                if img_path.exists():
                    image_file = str(img_path)
                    break
            
            # 测试改进算法
            start_time = time.time()
            
            optimizer = PerspectiveAwareOptimizer(
                tolerance=3.0,
                preserve_perspective=True,
                adaptive_threshold=True
            )
            
            output_file = f"perf_test_{test_file.name}"
            result = optimizer.optimize_table_annotation(
                str(test_file), 
                output_file, 
                image_file
            )
            
            processing_time = time.time() - start_time
            
            if result['success']:
                print(f"  ✅ 处理成功")
                print(f"  ⏱️  处理时间: {processing_time:.2f}秒")
                print(f"  📊 单元格数量: {result['cell_count']}")
                if result.get('adaptive_threshold'):
                    print(f"  🎯 自适应阈值: {result['adaptive_threshold']:.2f}")
                
                results.append({
                    'file': test_file.name,
                    'success': True,
                    'time': processing_time,
                    'cells': result['cell_count'],
                    'threshold': result.get('adaptive_threshold')
                })
                
                # 清理测试文件
                os.remove(output_file)
                
            else:
                print(f"  ❌ 处理失败: {result['error']}")
                results.append({
                    'file': test_file.name,
                    'success': False,
                    'error': result['error']
                })
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
            results.append({
                'file': test_file.name,
                'success': False,
                'error': str(e)
            })
    
    # 统计结果
    successful = [r for r in results if r['success']]
    print(f"\n性能测试结果: {len(successful)}/{len(results)} 成功")
    
    if successful:
        avg_time = sum(r['time'] for r in successful) / len(successful)
        avg_cells = sum(r['cells'] for r in successful) / len(successful)
        print(f"平均处理时间: {avg_time:.2f}秒")
        print(f"平均单元格数: {avg_cells:.1f}")
    
    return len(successful) > 0


def test_quality_assessment():
    """测试质量评估功能"""
    print("\n" + "=" * 60)
    print("测试质量评估功能")
    print("=" * 60)
    
    # 选择一个测试文件
    test_files = list(Path("organized_dataset").glob("*_table_annotation.json"))
    
    if not test_files:
        print("❌ 未找到测试文件")
        return False
    
    test_file = test_files[0]
    print(f"测试文件: {test_file.name}")
    
    try:
        # 查找对应图片
        image_file = None
        for ext in ['.jpg', '.jpeg', '.png']:
            img_path = test_file.parent / f"{test_file.stem.replace('_table_annotation', '')}{ext}"
            if img_path.exists():
                image_file = str(img_path)
                break
        
        # 创建优化器并处理
        optimizer = PerspectiveAwareOptimizer(
            tolerance=3.0,
            preserve_perspective=True,
            adaptive_threshold=True
        )
        
        output_file = f"quality_test_{test_file.name}"
        result = optimizer.optimize_table_annotation(
            str(test_file), 
            output_file, 
            image_file
        )
        
        if result['success']:
            print("✅ 质量评估功能正常工作")
            
            # 清理测试文件
            os.remove(output_file)
            return True
        else:
            print(f"❌ 处理失败: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🔧 改进后的表格标注优化器测试")
    print("=" * 60)
    
    # 检查测试环境
    if not Path("organized_dataset").exists():
        print("❌ 测试数据目录 'organized_dataset' 不存在")
        return 1
    
    # 运行测试
    test_results = []
    
    # 测试1: 属性保护
    test_results.append(("属性保护", test_attribute_preservation()))
    
    # 测试2: 算法性能
    test_results.append(("算法性能", test_algorithm_performance()))
    
    # 测试3: 质量评估
    test_results.append(("质量评估", test_quality_assessment()))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(test_results)} 测试通过")
    
    if passed == len(test_results):
        print("🎉 所有测试通过！改进后的算法工作正常。")
        return 0
    else:
        print("⚠️  部分测试失败，需要进一步调试。")
        return 1


if __name__ == "__main__":
    exit(main())
