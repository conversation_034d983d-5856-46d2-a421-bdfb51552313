#!/usr/bin/env python3
"""
测试质量感知优化的改进效果

对比传统算法和质量感知算法在合格样本上的表现
"""

import os
import json
import time
from pathlib import Path
from table_annotation_optimizer import PerspectiveAwareOptimizer


def test_quality_aware_vs_traditional():
    """对比质量感知算法和传统算法"""
    print("🔬 质量感知算法 vs 传统算法对比测试")
    print("=" * 60)
    
    # 选择几个合格样本进行测试
    test_samples = [
        "115919662_table_annotation.json",  # 高规整度，低对齐误差
        "1wizqck4_table_annotation.json",   # 大表格，多单元格
        "_cevaua3_table_annotation.json",   # 完美对齐样本
        "30v8x2na_yjc_table_annotation.json",  # 低对齐误差样本
        "21504950_table_annotation.json",   # 大表格样本
    ]
    
    results = []
    
    for sample_file in test_samples:
        file_path = Path("borderless_table") / sample_file
        if not file_path.exists():
            print(f"❌ 测试文件不存在: {sample_file}")
            continue
            
        print(f"\n测试样本: {sample_file}")
        print("-" * 50)
        
        # 查找对应图片
        image_file = None
        for ext in ['.jpg', '.jpeg', '.png']:
            img_path = file_path.parent / f"{file_path.stem.replace('_table_annotation', '')}{ext}"
            if img_path.exists():
                image_file = str(img_path)
                break
        
        # 测试传统算法
        print("🔧 传统算法:")
        traditional_result = test_algorithm(
            str(file_path), 
            image_file, 
            f"traditional_{sample_file}",
            quality_aware=False
        )
        
        # 测试质量感知算法
        print("🧠 质量感知算法:")
        quality_aware_result = test_algorithm(
            str(file_path), 
            image_file, 
            f"quality_aware_{sample_file}",
            quality_aware=True
        )
        
        # 对比结果
        comparison = compare_results(traditional_result, quality_aware_result, sample_file)
        results.append(comparison)
        
        # 清理测试文件
        cleanup_test_files([f"traditional_{sample_file}", f"quality_aware_{sample_file}"])
    
    # 总结对比结果
    print_summary(results)


def test_algorithm(annotation_file: str, image_file: str, output_file: str, quality_aware: bool) -> dict:
    """测试单个算法"""
    try:
        start_time = time.time()
        
        # 创建优化器
        optimizer = PerspectiveAwareOptimizer(
            tolerance=2.0,
            preserve_perspective=True,
            adaptive_threshold=True,
            quality_aware=quality_aware
        )
        
        # 执行优化
        result = optimizer.optimize_table_annotation(
            annotation_file,
            output_file,
            image_file
        )
        
        processing_time = time.time() - start_time
        
        if result['success']:
            print(f"  ✅ 处理成功")
            print(f"  ⏱️  处理时间: {processing_time:.3f}秒")
            print(f"  📊 单元格数: {result['cell_count']}")
            if result.get('adaptive_threshold'):
                print(f"  🎯 自适应阈值: {result['adaptive_threshold']:.2f}px")
            
            return {
                'success': True,
                'processing_time': processing_time,
                'cell_count': result['cell_count'],
                'adaptive_threshold': result.get('adaptive_threshold'),
                'output_file': output_file
            }
        else:
            print(f"  ❌ 处理失败: {result['error']}")
            return {
                'success': False,
                'error': result['error']
            }
            
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return {
            'success': False,
            'error': str(e)
        }


def compare_results(traditional: dict, quality_aware: dict, sample_name: str) -> dict:
    """对比两种算法的结果"""
    print(f"\n📊 {sample_name} 对比结果:")
    
    comparison = {
        'sample': sample_name,
        'traditional_success': traditional.get('success', False),
        'quality_aware_success': quality_aware.get('success', False)
    }
    
    if traditional.get('success') and quality_aware.get('success'):
        # 处理时间对比
        traditional_time = traditional['processing_time']
        quality_aware_time = quality_aware['processing_time']
        time_improvement = ((traditional_time - quality_aware_time) / traditional_time) * 100
        
        print(f"  ⏱️  处理时间:")
        print(f"     传统算法: {traditional_time:.3f}秒")
        print(f"     质量感知: {quality_aware_time:.3f}秒")
        print(f"     改进: {time_improvement:+.1f}%")
        
        # 阈值对比
        traditional_threshold = traditional.get('adaptive_threshold', 'N/A')
        quality_aware_threshold = quality_aware.get('adaptive_threshold', 'N/A')
        
        print(f"  🎯 自适应阈值:")
        print(f"     传统算法: {traditional_threshold}")
        print(f"     质量感知: {quality_aware_threshold}")
        
        comparison.update({
            'traditional_time': traditional_time,
            'quality_aware_time': quality_aware_time,
            'time_improvement': time_improvement,
            'traditional_threshold': traditional_threshold,
            'quality_aware_threshold': quality_aware_threshold
        })
        
        # 质量评估（如果有输出文件）
        if 'output_file' in traditional and 'output_file' in quality_aware:
            quality_comparison = compare_output_quality(
                traditional['output_file'],
                quality_aware['output_file']
            )
            comparison.update(quality_comparison)
    
    else:
        if not traditional.get('success'):
            print(f"  ❌ 传统算法失败: {traditional.get('error', '未知错误')}")
        if not quality_aware.get('success'):
            print(f"  ❌ 质量感知算法失败: {quality_aware.get('error', '未知错误')}")
    
    return comparison


def compare_output_quality(traditional_file: str, quality_aware_file: str) -> dict:
    """对比输出文件的质量"""
    try:
        # 这里可以添加更详细的质量对比逻辑
        # 比如角点对齐精度、规整度等
        print(f"  📈 质量对比: 输出文件已生成，可进行详细分析")
        return {'quality_comparison': 'available'}
    except Exception as e:
        print(f"  ⚠️  质量对比失败: {e}")
        return {'quality_comparison': 'failed'}


def cleanup_test_files(file_list: list):
    """清理测试文件"""
    for file_path in file_list:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception:
            pass  # 忽略清理错误


def print_summary(results: list):
    """打印总结报告"""
    print("\n" + "=" * 60)
    print("📊 质量感知优化总结报告")
    print("=" * 60)
    
    successful_comparisons = [r for r in results if r.get('traditional_success') and r.get('quality_aware_success')]
    
    if not successful_comparisons:
        print("❌ 没有成功的对比结果")
        return
    
    print(f"成功对比样本数: {len(successful_comparisons)}")
    
    # 处理时间统计
    time_improvements = [r['time_improvement'] for r in successful_comparisons if 'time_improvement' in r]
    if time_improvements:
        avg_time_improvement = sum(time_improvements) / len(time_improvements)
        print(f"\n⏱️  平均处理时间改进: {avg_time_improvement:+.1f}%")
        
        if avg_time_improvement > 0:
            print("✅ 质量感知算法在处理速度上有优势")
        elif avg_time_improvement < -10:
            print("⚠️  质量感知算法处理时间较长，但可能提供更好的质量")
        else:
            print("➡️  两种算法处理时间相近")
    
    # 成功率统计
    traditional_success_rate = sum(1 for r in results if r.get('traditional_success')) / len(results) * 100
    quality_aware_success_rate = sum(1 for r in results if r.get('quality_aware_success')) / len(results) * 100
    
    print(f"\n📈 成功率对比:")
    print(f"传统算法: {traditional_success_rate:.1f}%")
    print(f"质量感知: {quality_aware_success_rate:.1f}%")
    
    if quality_aware_success_rate > traditional_success_rate:
        print("✅ 质量感知算法成功率更高")
    elif quality_aware_success_rate == traditional_success_rate:
        print("➡️  两种算法成功率相同")
    else:
        print("⚠️  传统算法成功率更高")
    
    print(f"\n🎯 主要改进:")
    print("1. 基于97个合格样本的分析优化")
    print("2. 重叠角点检测和解决")
    print("3. 基于规整度的分组微调")
    print("4. 质量感知的自适应阈值")
    print("5. 更好的属性保护机制")


def main():
    """主函数"""
    print("🧪 质量感知优化改进效果测试")
    print("=" * 60)
    
    # 检查测试环境
    if not Path("borderless_table").exists():
        print("❌ 测试数据目录 'borderless_table' 不存在")
        return 1
    
    # 运行对比测试
    test_quality_aware_vs_traditional()
    
    print(f"\n🎉 测试完成！")
    print("质量感知优化基于97个合格样本的分析，针对性地解决了:")
    print("- 重叠角点问题（最小距离0.00px）")
    print("- 较大对齐误差（平均9.01px）")
    print("- 高规整度表格的精确处理（0.98+）")
    
    return 0


if __name__ == "__main__":
    exit(main())
