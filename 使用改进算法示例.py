#!/usr/bin/env python3
"""
使用改进后的表格标注优化器示例

展示如何使用改进后的算法，包括：
1. 属性保护功能
2. 改进的自适应阈值
3. 质量评估
"""

import os
from pathlib import Path
from table_annotation_optimizer import PerspectiveAwareOptimizer, BatchProcessor


def single_file_example():
    """单文件处理示例"""
    print("=" * 60)
    print("单文件处理示例")
    print("=" * 60)
    
    # 选择一个测试文件
    test_files = list(Path("organized_dataset").glob("*_table_annotation.json"))
    if not test_files:
        print("❌ 未找到测试文件")
        return
    
    test_file = test_files[0]
    print(f"处理文件: {test_file}")
    
    # 查找对应图片
    image_file = None
    for ext in ['.jpg', '.jpeg', '.png']:
        img_path = test_file.parent / f"{test_file.stem.replace('_table_annotation', '')}{ext}"
        if img_path.exists():
            image_file = str(img_path)
            break
    
    # 创建优化器
    optimizer = PerspectiveAwareOptimizer(
        tolerance=3.0,              # 基础阈值
        preserve_perspective=True,   # 保持透视变换
        adaptive_threshold=True      # 使用自适应阈值
    )
    
    # 执行优化
    output_file = f"improved_{test_file.name}"
    result = optimizer.optimize_table_annotation(
        str(test_file), 
        output_file, 
        image_file
    )
    
    if result['success']:
        print(f"✅ 处理成功！")
        print(f"📁 输出文件: {output_file}")
        print(f"📊 处理了 {result['cell_count']} 个单元格")
        if result.get('adaptive_threshold'):
            print(f"🎯 使用自适应阈值: {result['adaptive_threshold']:.2f}px")
    else:
        print(f"❌ 处理失败: {result['error']}")


def batch_processing_example():
    """批量处理示例"""
    print("\n" + "=" * 60)
    print("批量处理示例")
    print("=" * 60)
    
    # 配置批量处理参数
    config = {
        'input_dir': "F:\workspace\datasets\Relabel_TabRecSet\chinese\curved_tables",
        'output_dir': "F:\workspace\datasets\Relabel_TabRecSet\chinese\curved_tables_1",
        'tolerance': 3.0,
        'preserve_perspective': True,
        'adaptive_threshold': True,
        'max_workers': 4,
        'copy_images': True,
        'preserve_attributes': ['quality', 'type']  # 保护这些属性
    }
    
    print(f"输入目录: {config['input_dir']}")
    print(f"输出目录: {config['output_dir']}")
    print(f"基础阈值: {config['tolerance']}px")
    print(f"自适应阈值: {'启用' if config['adaptive_threshold'] else '禁用'}")
    print(f"保持透视: {'是' if config['preserve_perspective'] else '否'}")
    print(f"并行线程: {config['max_workers']}")
    
    # 创建批量处理器
    processor = BatchProcessor(config)
    
    # 执行批量处理
    stats = processor.process_batch()
    
    if stats.get('success', True):
        print(f"\n✅ 批量处理完成！")
        print(f"📊 处理统计:")
        print(f"   总文件数: {stats['total_files']}")
        print(f"   成功处理: {stats['successful']}")
        print(f"   处理失败: {stats['failed']}")
        print(f"   成功率: {stats['successful']/stats['total_files']*100:.1f}%")
        print(f"⏱️  总处理时间: {stats['total_time']:.1f}秒")
    else:
        print(f"❌ 批量处理失败: {stats.get('error', '未知错误')}")


def custom_config_example():
    """自定义配置示例"""
    print("\n" + "=" * 60)
    print("自定义配置示例")
    print("=" * 60)
    
    # 高精度配置（适合高分辨率图片）
    high_precision_config = {
        'input_dir': 'organized_dataset',
        'output_dir': 'high_precision_output',
        'tolerance': 1.5,           # 更小的基础阈值
        'preserve_perspective': True,
        'adaptive_threshold': True,
        'max_workers': 2,           # 减少并行数以提高精度
        'copy_images': False,       # 不复制图片
    }
    
    # 快速处理配置（适合大批量处理）
    fast_processing_config = {
        'input_dir': 'organized_dataset',
        'output_dir': 'fast_output',
        'tolerance': 5.0,           # 更大的基础阈值
        'preserve_perspective': True,
        'adaptive_threshold': True,
        'max_workers': 8,           # 更多并行线程
        'copy_images': True,
    }
    
    print("配置选项:")
    print("1. 高精度配置 - 适合高分辨率图片，追求最佳质量")
    print("2. 快速处理配置 - 适合大批量处理，平衡速度和质量")
    
    # 这里可以根据需要选择配置
    selected_config = high_precision_config
    print(f"\n使用配置: 高精度配置")
    print(f"基础阈值: {selected_config['tolerance']}px")
    print(f"并行线程: {selected_config['max_workers']}")


def quality_comparison_example():
    """质量对比示例"""
    print("\n" + "=" * 60)
    print("质量对比示例")
    print("=" * 60)
    
    # 选择一个测试文件
    test_files = list(Path("organized_dataset").glob("*_table_annotation.json"))
    if not test_files:
        print("❌ 未找到测试文件")
        return
    
    test_file = test_files[0]
    print(f"对比文件: {test_file.name}")
    
    # 查找对应图片
    image_file = None
    for ext in ['.jpg', '.jpeg', '.png']:
        img_path = test_file.parent / f"{test_file.stem.replace('_table_annotation', '')}{ext}"
        if img_path.exists():
            image_file = str(img_path)
            break
    
    # 测试不同配置
    configs = [
        ("保守配置", {"tolerance": 1.0, "adaptive_threshold": False}),
        ("标准配置", {"tolerance": 3.0, "adaptive_threshold": True}),
        ("宽松配置", {"tolerance": 6.0, "adaptive_threshold": True}),
    ]
    
    for config_name, config in configs:
        print(f"\n{config_name}:")
        
        optimizer = PerspectiveAwareOptimizer(
            tolerance=config['tolerance'],
            preserve_perspective=True,
            adaptive_threshold=config['adaptive_threshold']
        )
        
        output_file = f"compare_{config_name.replace('配置', '')}_{test_file.name}"
        result = optimizer.optimize_table_annotation(
            str(test_file), 
            output_file, 
            image_file
        )
        
        if result['success']:
            print(f"  ✅ 处理成功")
            if result.get('adaptive_threshold'):
                print(f"  🎯 自适应阈值: {result['adaptive_threshold']:.2f}px")
            else:
                print(f"  🎯 固定阈值: {config['tolerance']}px")
            
            # 清理测试文件
            if os.path.exists(output_file):
                os.remove(output_file)
        else:
            print(f"  ❌ 处理失败: {result['error']}")


def main():
    """主函数"""
    print("🔧 改进后的表格标注优化器使用示例")
    print("=" * 60)
    
    # 检查环境
    if not Path("organized_dataset").exists():
        print("❌ 测试数据目录 'organized_dataset' 不存在")
        print("请确保有测试数据后再运行此示例")
        return 1
    
    try:
        # 运行各种示例
        single_file_example()
        
        # 注释掉批量处理以避免创建大量文件
        # batch_processing_example()
        
        custom_config_example()
        quality_comparison_example()
        
        print("\n" + "=" * 60)
        print("示例运行完成！")
        print("=" * 60)
        print("主要改进:")
        print("✅ 完整的属性保护 - 保持原始文件的所有属性不变")
        print("✅ 改进的自适应阈值 - 基于图片和表格特征智能调整")
        print("✅ 多级微调策略 - 精确和中等两级微调")
        print("✅ 质量评估功能 - 实时评估对齐质量")
        print("✅ 动态聚类算法 - 更智能的角点分组")
        print("✅ 加权平均对齐 - 考虑单元格重要性的对齐")
        
        return 0
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
